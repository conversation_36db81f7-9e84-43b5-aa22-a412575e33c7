syntax = "proto3";

// 定义包名，这有助于防止不同 proto 文件之间的命名冲突
// 格式建议为：模块名}.{版本号}
package user.v1;

// 为生成的 Go 代码指定包路径和包名
// 格式为：{仓库路径}/api/{模块名}/{版本号};{版本号}
option go_package = "user-service/api/user/v1;v1";

// 导入 google 的 http 注解定义，这样我们才能定义 HTTP 接口
import "google/api/annotations.proto";

// 用户信息结构
message User{
    int64 user_id = 1;
    string user_name = 2;
    string nick_name = 3;
}

// 创建用户请求
message CreateUserRequest{
    string username = 1;
    string nickname = 2;
}

// 创建用户的响应
message CreateUserReply{
    int64 user_id = 1;    
}

// 获取用户信息的请求
message GetUserRequest{
    int64 user_id = 1;
}

// 获取用户信息的响应
message GetUserReply{
    User user = 1;
}

// 定义用户服务
service UserService {

    // RPC方法：创建用户
    rpc CreateUser(CreateUserRequest) returns (CreateUserReply){
        // HTTP 注解
        option (google.api.http) = {
            // 映射为 HTTP POST 请求
            post: "/V1/users"
            // 将 HTTP 请求的body 映射到 CreateUserRequest
            body: "*"
        };
    }

    // RPC方法：获取用户信息
    rpc GetUser(GetUserRequest) returns (GetUserReply){
        // HTTP 注解
        option (google.api.http) = {
            // 映射为 HTTP GET 请求
            get: "/V1/users/{user_id}" // URL 路由中的{ user_id }会自动映射到GetUserRequest 中的 user_id 
        };
    }
}