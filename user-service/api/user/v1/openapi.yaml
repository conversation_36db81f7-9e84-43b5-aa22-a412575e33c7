# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: UserService API
    description: 定义用户服务
    version: 0.0.1
paths:
    /V1/users:
        post:
            tags:
                - UserService
            description: RPC方法：创建用户
            operationId: UserService_CreateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateUserReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /V1/users/{userId}:
        get:
            tags:
                - UserService
            description: RPC方法：获取用户信息
            operationId: UserService_GetUser
            parameters:
                - name: userId
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CreateUserReply:
            type: object
            properties:
                userId:
                    type: string
            description: 创建用户的响应
        CreateUserRequest:
            type: object
            properties:
                username:
                    type: string
                nickname:
                    type: string
            description: 创建用户请求
        GetUserReply:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/User'
            description: 获取用户信息的响应
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        User:
            type: object
            properties:
                userId:
                    type: string
                userName:
                    type: string
                nickName:
                    type: string
            description: 用户信息结构
tags:
    - name: UserService
