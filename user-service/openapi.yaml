# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /V1/users:
        post:
            tags:
                - UserService
            description: RPC方法：创建用户
            operationId: UserService_CreateUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.CreateUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.CreateUserReply'
    /V1/users/{userId}:
        get:
            tags:
                - UserService
            description: RPC方法：获取用户信息
            operationId: UserService_GetUser
            parameters:
                - name: userId
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.GetUserReply'
    /helloworld/{name}:
        get:
            tags:
                - Greeter
            description: Sends a greeting
            operationId: <PERSON>reet<PERSON>_<PERSON><PERSON><PERSON>
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/helloworld.v1.HelloReply'
components:
    schemas:
        helloworld.v1.HelloReply:
            type: object
            properties:
                message:
                    type: string
            description: The response message containing the greetings
        user.v1.CreateUserReply:
            type: object
            properties:
                userId:
                    type: string
            description: 创建用户的响应
        user.v1.CreateUserRequest:
            type: object
            properties:
                username:
                    type: string
                nickname:
                    type: string
            description: 创建用户请求
        user.v1.GetUserReply:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/user.v1.User'
            description: 获取用户信息的响应
        user.v1.User:
            type: object
            properties:
                userId:
                    type: string
                userName:
                    type: string
                nickName:
                    type: string
            description: 用户信息结构
tags:
    - name: Greeter
      description: The greeting service definition.
    - name: UserService
      description: 定义用户服务
