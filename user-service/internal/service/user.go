package service

// 导入我们刚刚生成的 pb.go 文件所在的包
// go.mod 中定义的 module name 是 user-service
import (
	"context"
	v1 "user-service/api/user/v1"
	"user-service/internal/biz"
)

type UserService struct {
	// Kratos 强制要求 Service struct 必须嵌入 UnimplementedXXXServer
	// 这是为了向前兼容，保证在 proto 文件新增接口后，旧的 service 代码不会编译失败
	v1.UnimplementedUserServiceServer

	uc *biz.UserUsecase
}

func NewUserService(uc *biz.UserUsecase) *UserService {
	return &UserService{uc: uc}
}

func (s *UserService) CreateUser(ctx context.Context, req *v1.CreateUserRequest) (*v1.CreateUserReply, error) {
	// fmt.Println("Username: ",req.Username)
	// fmt.Println("Nickname: ",req.Nickname)
	user, err := s.uc.CreateUser(ctx, req.Username, req.Nickname)
	if err != nil {
		return nil, err
	}
	return &v1.CreateUserReply{
		UserId: user.ID,
	}, nil
}
