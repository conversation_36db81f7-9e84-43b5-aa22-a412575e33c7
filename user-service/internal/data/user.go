package data

import (
	"context"
	"user-service/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type userRepo struct {
	data *Data
	log  *log.Helper
}

func NewUserRepo(data *Data, logger *log.Helper) biz.UserRepo {
	return &userRepo{
		data: data,
		log:  logger,
	}
}

func (r *userRepo) Create(ctx context.Context, u *biz.User) (*biz.User, error) {
	r.log.Infof("CreateUser in data, username: %s, nickname: %s", u.Username, u.Nickname)

	u.ID = 123
	return u, nil
}
