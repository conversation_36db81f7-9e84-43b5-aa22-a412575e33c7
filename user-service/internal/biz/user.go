package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

type User struct {
	ID       int64
	Username string
	Nickname string
}

// UserRepo biz层与data层接口
type UserRepo interface {
	// 创建用户
	Create(ctx context.Context, u *User) (*User, error)
}

type UserUsecase struct {
	// Biz 层通常会依赖 Data 层的接口
	repo UserRepo
	log  *log.Helper
}

func NewUserUsecase(repo UserRepo, logger log.Logger) *UserUsecase {
	return &UserUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// 这就是我们的核心业务方法
func (uc *UserUsecase) CreateUser(ctx context.Context, username, nickname string) (*User, error) {
	uc.log.Infof("CreateUser in biz , username : %s , nickname : %s", username, nickname)
	// 在这里执行核心业务
	// 例如：调用 repo.CreateUser...
	user,err:=uc.repo.Create(ctx,&User{
		Username: username,
		Nickname: nickname,
	})
	if err!=nil{
		return nil,err
	}

	return user, nil
}
